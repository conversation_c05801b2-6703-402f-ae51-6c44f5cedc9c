<?php

namespace App\View\Components;

use Domain\Cart\Cart as CartService;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;
use Illuminate\View\View;

class AppLayout extends Component
{
    public $cartItems;

    public function __construct()
    {
        // Get cart data once for the entire layout
        if (Auth::check()) {
            $cartData = CartService::getUserCart();
            $this->cartItems = $cartData['items'];
        } else {
            $this->cartItems = [];
        }
    }

    /**
     * Get the view / contents that represents the component.
     */
    public function render(): View
    {
        return view('components.layouts.app', [
            'cartItems' => $this->cartItems,
        ]);
    }
}
