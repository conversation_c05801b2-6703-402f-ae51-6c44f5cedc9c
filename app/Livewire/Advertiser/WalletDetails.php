<?php

namespace App\Livewire\Advertiser;

use Bavix\Wallet\Models\Transaction;
use Domain\Cart\Cart as CartService;
use Livewire\Component;

class WalletDetails extends Component
{
    public $id;

    public function mount($id): void
    {
        $this->id = $id;
    }

    public function getTransactionProperty()
    {
        return Transaction::find($this->id);
    }



    public function render()
    {
        // Get cart data for header
        $cartData = CartService::getUserCart();
        $cartItems = $cartData['items'];

        return view('livewire.advertiser.wallet-details', [
            'cartItems' => $cartItems,
        ])->layout('components.layouts.app', [
            'cartItems' => $cartItems,
        ]);
    }
}
