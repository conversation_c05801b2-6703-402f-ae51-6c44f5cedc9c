<?php

namespace App\Livewire\Advertiser;

use Domain\Cart\Cart as CartService;
use Domain\Wallet\Resources\TransitionResource;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Wallet extends Component
{
    public function mount(): void {}

    public function getBalanceProperty()
    {
        $balance = Auth::user()->user_balance;

        if (!is_string($balance)) {
            return config('pressbear.currency_symbol') . number_format($balance, 2);
        } else {
            return config('pressbear.currency_symbol') . $balance;
        }
    }

    public function getTransactionsProperty()
    {
        $transactions = Auth::user()->wallet->transactions()
            ->with('wallet', 'wallet.holder')
            ->orderBy('id', 'desc')
            ->get();

        return TransitionResource::collection($transactions)->resolve();
    }



    public function render()
    {
        // Get cart data for header
        $cartData = CartService::getUserCart();
        $cartItems = $cartData['items'];

        return view('livewire.advertiser.wallet', [
            'cartItems' => $cartItems,
        ]);
    }
}
