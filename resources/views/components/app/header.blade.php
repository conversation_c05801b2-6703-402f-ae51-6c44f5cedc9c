<div id="app-head" class="bg-white px-0 md:px-12 py-5 border-b max-w-full">
    <div class="flex items-center ">


        {{-- **** --}}
        {{-- logo --}}
        <a href="{{route('marketplace')}}" id="head-logo" wire:navigate
            class=" flex items-center px-3 md:mr-8 cursor-pointer hover:text-muted-foreground text-slate-700">
            {{--
            <x-icons.etc.link /> --}}

            <x-icons.etc.bear class="w-6 h-6 stroke-2" />
            <span class="ml-1 text-xs md:text-sm sm:text-lg font-extrabold uppercase text-md [letter-spacing:1px]">{{
                __('app_name') }}</span>
        </a>


        {{-- ******** --}}
        {{-- Top Menu --}}
        <div class="">
            <x-marketplace.header.menu-top />
        </div>


        {{-- ******************* --}}
        {{-- Command Search Menu --}}
        {{-- <div class="ml-auto hidden lg:flex px-2">
            <x-marketplace.header.command-menu />
        </div> --}}



        {{-- ****************** --}}
        {{-- Right Menu Section --}}
        <div id="right-top-menu-section" class="flex ml-auto items-center space-x-2 md:space-x-4">

            {{--
            @php
            $cartItems = $cartItems ?? [];
            @endphp
            --}}


            {{-- Language Switcher --}}
            {{--
            <x-marketplace.header.language-switcher /> --}}

            {{-- CART ICON --}}
            @if(isset($cartItems))
                @dd($cartItems)
            <div id="cartPreviewTop">
                @can('advertiser')
                <x-marketplace.header.cart-info-panel :cartItems="$cartItems" />
                @endcan
            </div>
            @endif

            {{-- Wallet icon --}}
            @can('advertiser')
            <a href="{{route('advertiser.wallet.index')}}" wire:navigate
                class="p-3 hover:bg-gray-100 rounded-full cursor-pointer">
                <x-icons.etc.wallet />
            </a>
            @endcan

            @can('advertiser')
            {{-- Notification panel --}}
            <div class="ml-auto  md:flex">
                <livewire:marketplace.notifications-panel />
            </div>
            @endcan

            {{-- Profile dropdown --}}
            <x-marketplace.header.user-profile-dropdown />

        </div>
    </div>


</div>


<x-app.banner />